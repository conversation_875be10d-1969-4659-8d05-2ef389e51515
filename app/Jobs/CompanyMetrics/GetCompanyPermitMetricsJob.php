<?php

namespace App\Jobs;

use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class GetCompanyPermitMetricsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * US State codes for all 50 states
     */
    const US_STATES = [
        'AL', // Alabama
        'AK', // Alaska
        'AZ', // Arizona
        'AR', // Arkansas
        'CA', // California
        'CO', // Colorado
        'CT', // Connecticut
        'DE', // Delaware
        'FL', // Florida
        'GA', // Georgia
        'HI', // Hawaii
        'ID', // Idaho
        'IL', // Illinois
        'IN', // Indiana
        'IA', // Iowa
        'KS', // Kansas
        'KY', // Kentucky
        'LA', // Louisiana
        'ME', // Maine
        'MD', // Maryland
        'MA', // Massachusetts
        'MI', // Michigan
        'MN', // Minnesota
        'MS', // Mississippi
        'MO', // Missouri
        'MT', // Montana
        'NE', // Nebraska
        'NV', // Nevada
        'NH', // New Hampshire
        'NJ', // New Jersey
        'NM', // New Mexico
        'NY', // New York
        'NC', // North Carolina
        'ND', // North Dakota
        'OH', // Ohio
        'OK', // Oklahoma
        'OR', // Oregon
        'PA', // Pennsylvania
        'RI', // Rhode Island
        'SC', // South Carolina
        'SD', // South Dakota
        'TN', // Tennessee
        'TX', // Texas
        'UT', // Utah
        'VT', // Vermont
        'VA', // Virginia
        'WA', // Washington
        'WV', // West Virginia
        'WI', // Wisconsin
        'WY', // Wyoming
    ];

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected ?string $apiKey = null,
        protected ?string $dateFrom = null,
        protected ?string $dateTo = null,
        protected array $permitTags = []
    ) {
        $this->onQueue(QueueHelperService::QUEUE_NAME_DATA_ENRICHMENT);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get API key from config if not provided
            $apiKey = $this->apiKey ?? config('services.shovels.api_key');

            if (!$apiKey) {
                throw new \Exception('Shovels API key not found. Please set SHOVELS_API_KEY in environment or pass as parameter.');
            }

            // Set default date range if not provided (previous month)
            $dateFrom = $this->dateFrom ?? Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');
            $dateTo = $this->dateTo ?? Carbon::now()->subMonth()->endOfMonth()->format('Y-m-d');

            Log::info("Dispatching ImportLocationPermitMetrics jobs for all 50 US states", [
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'permit_tags' => $this->permitTags,
                'total_states' => count(self::US_STATES)
            ]);

            $dispatchedCount = 0;

            // Dispatch a job for each state
            foreach (self::US_STATES as $stateCode) {
                ImportLocationPermitMetricsForStateJob::dispatch(
                    $stateCode,
                    $apiKey,
                    $dateFrom,
                    $dateTo,
                    $this->permitTags
                );

                $dispatchedCount++;

                Log::info("Dispatched ImportLocationPermitMetrics job for state: {$stateCode}");
            }

            Log::info("Successfully dispatched ImportLocationPermitMetrics jobs for all {$dispatchedCount} states");

        } catch (\Exception $e) {
            Log::error("Error dispatching monthly location permit metrics jobs: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("DispatchMonthlyLocationPermitMetricsJob failed: " . $exception->getMessage());
    }
}
