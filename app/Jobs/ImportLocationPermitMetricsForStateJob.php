<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class ImportLocationPermitMetricsForStateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600; // 1 hour

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected string $stateCode,
        protected string $apiKey,
        protected ?string $dateFrom = null,
        protected ?string $dateTo = null,
        protected array $permitTags = []
    ) {
        $this->onQueue(config('queue.named_queues.long_running', 'default'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting ImportLocationPermitMetrics for state: {$this->stateCode}");

            // Build the command arguments
            $arguments = [
                'api_key' => $this->apiKey,
                'geo_id' => $this->stateCode,
            ];

            // Add optional parameters if provided
            if ($this->dateFrom) {
                $arguments['--date-from'] = $this->dateFrom;
            }

            if ($this->dateTo) {
                $arguments['--date-to'] = $this->dateTo;
            }

            if (!empty($this->permitTags)) {
                $arguments['--permit-tags'] = $this->permitTags;
            }

            // Execute the command
            $exitCode = Artisan::call('app:import-location-permit-metrics', $arguments);

            if ($exitCode === 0) {
                Log::info("Successfully completed ImportLocationPermitMetrics for state: {$this->stateCode}");
            } else {
                Log::error("ImportLocationPermitMetrics failed for state: {$this->stateCode} with exit code: {$exitCode}");
                throw new \Exception("Command failed with exit code: {$exitCode}");
            }

        } catch (\Exception $e) {
            Log::error("Error running ImportLocationPermitMetrics for state {$this->stateCode}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ImportLocationPermitMetricsForStateJob failed for state {$this->stateCode}: " . $exception->getMessage());
    }
}
